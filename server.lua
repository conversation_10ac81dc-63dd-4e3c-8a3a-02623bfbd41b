local ESX = nil
local QBCore = nil

-- 根据配置初始化框架
if Config.Framework == 'esx' then
	ESX = exports["es_extended"]:getSharedObject()
elseif Config.Framework == 'qb' then
	QBCore = exports['qb-core']:GetCoreObject()
end

-- 添加获取在线医生数量的函数
local function GetOnlineDoctors()
	local xiaohaDoctors = 0
	
	if Config.Framework == 'esx' then
		local xiaohaPlayers = ESX.GetExtendedPlayers('job', 'ambulance')
		
		if Config.xiaohaDebug then
			print("获取在线医生数量: " .. #xiaohaPlayers)
		end
		
		for _, xiaohaPlayer in pairs(xiaohaPlayers) do
			if Config.xiaohaDebug then
				print("检查医生 " .. xiaohaPlayer.source)
			end
			xiaohaDoctors = xiaohaDoctors + 1
		end
	elseif Config.Framework == 'qb' then
		local xiaohaPlayers = QBCore.Functions.GetQBPlayers()
		
		if Config.xiaohaDebug then
			print("获取在线医生数量: " .. #xiaohaPlayers)
		end
		
		for _, xiaohaPlayer in pairs(xiaohaPlayers) do
			if xiaohaPlayer.PlayerData.job.name == "ambulance" then
				if Config.xiaohaDebug then
					print("检查医生 " .. xiaohaPlayer.PlayerData.source)
				end
				xiaohaDoctors = xiaohaDoctors + 1
			end
		end
	end
	
	if Config.xiaohaDebug then
		print("当前在线医生数量: " .. xiaohaDoctors)
		print("最大允许医生数量: " .. Config.xiaohaMaxDoctors)
	end
	return xiaohaDoctors
end

-- 注册回调函数
if Config.Framework == 'esx' then
	ESX.RegisterServerCallback('xiaohahenshuai:check', function(source, cb)
		handleCheck(source, cb)
	end)
elseif Config.Framework == 'qb' then
	QBCore.Functions.CreateCallback('xiaohahenshuai:check', function(source, cb)
		handleCheck(source, cb)
	end)
end

-- 处理检查逻辑
function handleCheck(source, cb)
	if Config.xiaohaDebug then
		print("收到检查请求，来源玩家: " .. source)
	end
	
	local xiaohaCanPay = false
	local xiaohaPlayer = nil
	
	if Config.Framework == 'esx' then
		xiaohaPlayer = ESX.GetPlayerFromId(source)
		if xiaohaPlayer.getMoney() >= Config.xiaohaPrice then
			xiaohaCanPay = true
		else
			if xiaohaPlayer.getAccount('bank').money >= Config.xiaohaPrice then
				xiaohaCanPay = true
			end
		end
	elseif Config.Framework == 'qb' then
		xiaohaPlayer = QBCore.Functions.GetPlayer(source)
		if xiaohaPlayer.PlayerData.money['cash'] >= Config.xiaohaPrice then
			xiaohaCanPay = true
		else
			if xiaohaPlayer.PlayerData.money['bank'] >= Config.xiaohaPrice then
				xiaohaCanPay = true
			end
		end
	end

	local xiaohaDoctors = GetOnlineDoctors()
	if Config.xiaohaDebug then
		print("检查回调 - 医生数量: " .. xiaohaDoctors)
		print("玩家支付能力: " .. tostring(xiaohaCanPay))
	end
	
	if xiaohaDoctors >= Config.xiaohaMaxDoctors then
		if Config.xiaohaDebug then
			print("医生数量超过限制，阻止使用AI医护")
		end
		TriggerClientEvent('xiaohahenshuai:notify', source, string.format(Config.xiaohahenshuaiNotify.too_many_doctors, Config.xiaohaMaxDoctors), 'error')
		cb(xiaohaDoctors, false)  -- 返回实际的医生数量和false
		return
	end
	
	cb(xiaohaDoctors, xiaohaCanPay)
end

RegisterServerEvent('xiaohahenshuai:charge', function()
	local source = source
	local xiaohaPrice = Config.xiaohaPrice
	local paymentType = Config.xiaohaPaymentType
	local xiaohaPlayer = nil

	if Config.xiaohaDebug then
		print("收到收费请求，来源玩家: " .. source)
	end

	-- 检查医生数量
	local xiaohaDoctors = GetOnlineDoctors()
	if Config.xiaohaDebug then
		print("收费事件 - 医生数量: " .. xiaohaDoctors)
	end
	
	if xiaohaDoctors >= Config.xiaohaMaxDoctors then
		if Config.xiaohaDebug then
			print("医生数量超过限制，阻止使用AI医护")
		end
		TriggerClientEvent('xiaohahenshuai:notify', source, string.format(Config.xiaohahenshuaiNotify.too_many_doctors, Config.xiaohaMaxDoctors), 'error')
		return
	end

	-- 获取玩家对象
	if Config.Framework == 'esx' then
		xiaohaPlayer = ESX.GetPlayerFromId(source)
	elseif Config.Framework == 'qb' then
		xiaohaPlayer = QBCore.Functions.GetPlayer(source)
	end

	-- 检查支付
	if Config.Framework == 'esx' then
		if xiaohaPlayer.getMoney() >= xiaohaPrice then
			-- 如果现金足够，优先使用现金支付
			xiaohaPlayer.removeMoney(xiaohaPrice)
			TriggerClientEvent('xiaohahenshuai:notify', source, string.format(Config.xiaohahenshuaiNotify.payment_success_cash, xiaohaPrice), 'success')
		elseif xiaohaPlayer.getAccount('bank').money >= xiaohaPrice then
			-- 如果现金不够但银行余额足够，使用银行支付
			xiaohaPlayer.removeAccountMoney('bank', xiaohaPrice)
			TriggerClientEvent('xiaohahenshuai:notify', source, string.format(Config.xiaohahenshuaiNotify.payment_success_bank, xiaohaPrice), 'success')
		else
			TriggerClientEvent('xiaohahenshuai:notify', source, Config.xiaohahenshuaiNotify.not_enough_money, 'error')
			return
		end

		-- ESX 公账处理
		if Config.xiaohaAddToSociety then
			TriggerEvent('esx_addonaccount:getSharedAccount', Config.xiaohaSocietyAccount.esx, function(account)
				account.addMoney(xiaohaPrice)
			end)
		end
	elseif Config.Framework == 'qb' then
		if xiaohaPlayer.PlayerData.money['cash'] >= xiaohaPrice then
			-- 如果现金足够，优先使用现金支付
			xiaohaPlayer.Functions.RemoveMoney('cash', xiaohaPrice)
			TriggerClientEvent('xiaohahenshuai:notify', source, string.format(Config.xiaohahenshuaiNotify.payment_success_cash, xiaohaPrice), 'success')
		elseif xiaohaPlayer.PlayerData.money['bank'] >= xiaohaPrice then
			-- 如果现金不够但银行余额足够，使用银行支付
			xiaohaPlayer.Functions.RemoveMoney('bank', xiaohaPrice)
			TriggerClientEvent('xiaohahenshuai:notify', source, string.format(Config.xiaohahenshuaiNotify.payment_success_bank, xiaohaPrice), 'success')
		else
			TriggerClientEvent('xiaohahenshuai:notify', source, Config.xiaohahenshuaiNotify.not_enough_money, 'error')
			return
		end

		-- QB-Core 公账处理
		if Config.xiaohaAddToSociety then
			if Config.xiaohaDebug then
				print("尝试添加金钱到QB-Core公账: " .. Config.xiaohaSocietyAccount.qb)
			end
			
			-- 尝试不同的方法添加金钱到公账
			local success = false
			
			-- 方法1: 使用qb-banking导出函数
			if exports['qb-banking'] and exports['qb-banking'].AddMoney then
				exports['qb-banking']:AddMoney(Config.xiaohaSocietyAccount.qb, xiaohaPrice)
				success = true
				if Config.xiaohaDebug then
					print("使用qb-banking AddMoney导出函数成功")
				end
			end
			
			-- 方法2: 使用qb-banking事件
			if not success and exports['qb-banking'] then
				TriggerEvent('qb-banking:server:addMoney', Config.xiaohaSocietyAccount.qb, xiaohaPrice)
				if Config.xiaohaDebug then
					print("使用qb-banking事件方法")
				end
			end
			
			-- 方法3: 使用qb-management事件
			if not success then
				TriggerEvent('qb-management:server:addMoney', Config.xiaohaSocietyAccount.qb, xiaohaPrice)
				if Config.xiaohaDebug then
					print("使用qb-management事件方法")
				end
			end
		else
			if Config.xiaohaDebug then
				print("公账功能已禁用，不添加金钱到公账")
			end
		end
	end

	if Config.xiaohaDebug then
		print("当前使用的通知系统: " .. Config.xiaohaNotifyType)
	end
end)

RegisterServerEvent('SRC:DeleteEntity', function(vehicleNetworkId)
	DeleteEntity(NetworkGetEntityFromNetworkId(vehicleNetworkId))
end)

RegisterServerEvent('xiaohahenshuai:chatNotify', function()
	if Config.xiaohaChatNotify then
		TriggerClientEvent('chatMessage', -1, 'AI医护系统', { 255, 0, 0 }, '您已经被AI医护治疗成功')
	end
end)
