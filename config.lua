Config = {
	Framework = 'esx',			-- 框架: 'esx' 或 'qb'
	Command = "120",				-- 复活指令
	xiaohaPrice = 2000,			-- 复活价格
	xiaohaReviveTime = 10000,		-- 复活时间(毫秒，例如5000=5秒)
	xiaohaCooldown = 0,			-- AI医护冷却时间(秒，例如600=10分钟)
	xiaohaMaxDoctors = 2,		-- 当在线医生数量小于此数值时，才能使用复活指令
	xiaohaPaymentType = 'cash',  -- 支付类型: 'cash', 'bank', 'black_money'
	xiaohaRevivalSystem = 'osp',  -- 复活系统: 'auto', 'osp', 'esx', 'qb'-- qb框架需要使用qb-banking插件    以及使用qb框架的话如果使用wasabi医护选择qb 需要手动选择qb

	-- 通知系统配置
	xiaohaNotifyType = 'auto',	-- 通知类型: 'auto', 'esx', 'qb', 'mythic', 'okokNotify', 'custom'
	
	-- 通知文本配置
	xiaohahenshuaiNotify = {
		revive_complete = "您已经被治疗，费用：%s",
		too_many_doctors = "当前在线医生数量已达到%s人或以上，请寻找在线医生救治",
		not_enough_money = "您的现金和银行账户余额都不足以支付治疗费用",
		cooldown = "AI医护冷却中，还需等待 %s 秒",
		only_dead = "只能在昏迷状态下使用",
		healing = "正在治疗中",
		payment_success_cash = "已从现金支付 $%d 治疗费用",
		payment_success_bank = "已从银行账户支付 $%d 治疗费用"
	},


	xiaohaNotifyReviveTime = true,  -- 是否启用复活时间通知
	xiaohaNotifyPayment = true,      -- 是否启用支付费用通知
	xiaohaDebug = false,  -- 启用调试模式

	-- 新增聊天框通知开关
	xiaohaChatNotify = true,  -- 是否启用聊天框通知

	-- 公账账户配置
	xiaohaSocietyAccount = {
		esx = 'society_ambulance',  -- ESX框架的公账账户名称
		qb = 'ambulance'  -- QB-Core框架的公账账户名称
	},

	-- 新增每秒通知开关
	-- xiaohaSecondBySecondNotify = true,  -- 是否启用每秒通知剩余复活时间

	xiaohaAddToSociety = true,  -- 是否将钱存入公账，设置为 false 则不存入公账
}
