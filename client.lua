local ESX = nil
local QBCore = nil

-- 根据配置初始化框架
if Config.Framework == 'esx' then
    ESX = exports["es_extended"]:getSharedObject()
elseif Config.Framework == 'qb' then
    QBCore = exports['qb-core']:GetCoreObject()
end

local xiaohaIsDead = false
local lastReviveTime = 0  -- 新增：记录上次使用AI医护的时间

-- 添加通知事件处理
RegisterNetEvent('xiaohahenshuai:notify')
AddEventHandler('xiaohahenshuai:notify', function(message, type)
    SendNotification(message, type)
end)

-- 玩家重生时重置状态
if Config.Framework == 'esx' then
    AddEventHandler('esx:onPlayerSpawn', function(spawn)
        xiaohaIsDead = false
    end)

    -- 玩家死亡时设置状态和冷却时间
    AddEventHandler('esx:onPlayerDeath', function(data)
        xiaohaIsDead = Config.xiaohaCooldown
        for i = Config.xiaohaCooldown, 0, -1 do
            if type(xiaohaIsDead) ~= "boolean" then
                xiaohaIsDead = i
            else
                return
            end
            Wait(1000)
        end
        if type(xiaohaIsDead) == "number" then
            xiaohaIsDead = true
        end
    end)
elseif Config.Framework == 'qb' then
    -- QB-Core 玩家状态事件
    RegisterNetEvent('hospital:client:Revive', function()
        xiaohaIsDead = false
    end)

    -- QB-Core 死亡事件
    RegisterNetEvent('hospital:client:Death', function()
        xiaohaIsDead = true
    end)
end

-- 新增死亡检测函数
function isDead()
    if Config.xiaohaRevivalSystem == 'osp' then
        local playerId = GetPlayerServerId(PlayerId())
        local ambulanceData = exports.osp_ambulance:GetAmbulanceData(playerId)
        if ambulanceData.isDead or ambulanceData.inLastStand then
            return true
        end
    elseif Config.Framework == 'qb' then
        local Player = QBCore.Functions.GetPlayerData()
        return Player.metadata["isdead"] or Player.metadata["inlaststand"]
    else -- esx system
        return xiaohaIsDead
    end
    return false
end 

-- 检查是否在冷却时间内
function isInCooldown()
    local currentTime = GetGameTimer()
    local timeSinceLastRevive = currentTime - lastReviveTime
    return timeSinceLastRevive < (Config.xiaohaCooldown * 1000)  -- 转换为毫秒
end

-- 获取复活系统类型
function GetRevivalSystem()
    if Config.xiaohaRevivalSystem == 'auto' then
        -- 自动检测复活系统
        if exports['osp_ambulance'] then
            return 'osp'
        elseif exports['esx_ambulancejob'] then
            return 'esx'
        elseif exports['wasabi_ambulance'] then
            return 'wasabi'
        elseif Config.Framework == 'qb' then
            return 'qb'
        else
            -- 默认使用框架自带的复活系统
            return Config.Framework
        end
    else
        return Config.xiaohaRevivalSystem
    end
end

-- 注册复活指令
RegisterCommand(Config.Command, function(source, args, raw)
    -- 检查是否在冷却时间内
    if isInCooldown() then
        local remainingTime = math.ceil((Config.xiaohaCooldown * 1000 - (GetGameTimer() - lastReviveTime)) / 1000)
        SendNotification(string.format(Config.xiaohahenshuaiNotify.cooldown, remainingTime))
        return
    end

    if isDead() then
        if Config.Framework == 'esx' then
            ESX.TriggerServerCallback('xiaohahenshuai:check', function(xiaohaDoctors, xiaohaCanPay)
                handleRevive(xiaohaDoctors, xiaohaCanPay)
            end)
        elseif Config.Framework == 'qb' then
            QBCore.Functions.TriggerCallback('xiaohahenshuai:check', function(xiaohaDoctors, xiaohaCanPay)
                handleRevive(xiaohaDoctors, xiaohaCanPay)
            end)
        end
    elseif type(xiaohaIsDead) == "number" then
        SendNotification(string.format(Config.xiaohahenshuaiNotify.cooldown, xiaohaIsDead))
    else
        SendNotification(Config.xiaohahenshuaiNotify.only_dead)
    end
end)

-- 处理复活逻辑
function handleRevive(xiaohaDoctors, xiaohaCanPay)
    if Config.xiaohaDebug then
        print("客户端收到医生数量: " .. xiaohaDoctors)
        print("客户端收到支付能力: " .. tostring(xiaohaCanPay))
    end
    
    if xiaohaDoctors >= Config.xiaohaMaxDoctors then
        SendNotification(string.format(Config.xiaohahenshuaiNotify.too_many_doctors, Config.xiaohaMaxDoctors), 'error')
        return
    end
    
    if not xiaohaCanPay then
        SendNotification(Config.xiaohahenshuaiNotify.not_enough_money, 'error')
        return
    end
    
    -- 先收费
    TriggerServerEvent('xiaohahenshuai:charge')
    
    -- 根据配置决定是否通知复活时间
    if Config.xiaohaNotifyReviveTime then
        SendNotification(string.format("复活将在 %s 秒后完成", Config.xiaohaReviveTime / 1000))
    end
    
    -- 通知支付类型
    local paymentType = Config.xiaohaPaymentType
    local paymentTypeText = paymentType == 'cash' and "现金" or (paymentType == 'bank' and "银行" or "黑钱")
    SendNotification(string.format("您使用了 %s 进行支付", paymentTypeText))
    
    if Config.xiaohaDebug then
        print("当前使用的通知系统: " .. Config.xiaohaNotifyType)
    end
    
    -- 创建一个变量记录开始时间
    local startTime = GetGameTimer()
    local lastNotificationTime = startTime
    
    -- 开始进度条
    if Config.Framework == 'esx' then
        ESX.Progressbar(Config.xiaohahenshuaiNotify.healing, Config.xiaohaReviveTime)
    elseif Config.Framework == 'qb' then
        QBCore.Functions.Progressbar("revive", Config.xiaohahenshuaiNotify.healing, Config.xiaohaReviveTime, false, true, {
            disableMovement = true,
            disableCarMovement = true,
            disableMouse = false,
            disableCombat = true,
        }, {}, {}, {}, function() -- Done
            local revivalSystem = GetRevivalSystem()
            if revivalSystem == 'osp' then
                TriggerEvent('osp_ambulance:revive')
            elseif revivalSystem == 'esx' then
                TriggerEvent('esx_ambulancejob:revive')
            elseif revivalSystem == 'wasabi' then
                TriggerEvent('wasabi_ambulance:revive')
            elseif revivalSystem == 'qb' then
                TriggerEvent('hospital:client:Revive')
            end
            StopScreenEffect('DeathFailOut')
            
            -- 记录使用AI医护的时间
            lastReviveTime = GetGameTimer()
            
            -- 根据配置决定是否通知支付费用
            if Config.xiaohaNotifyPayment then
                SendNotification(string.format(Config.xiaohahenshuaiNotify.revive_complete, Config.xiaohaPrice))
            end
            
            -- 新增聊天框通知
            if Config.xiaohaChatNotify then
                TriggerServerEvent('xiaohahenshuai:chatNotify')
            end
        end)
    end
    
    -- 创建一个线程来监控进度条完成（仅用于ESX）
    if Config.Framework == 'esx' then
        Citizen.CreateThread(function()
            while true do
                Wait(0)
                
                -- 检查是否达到设定时间
                local elapsedTime = GetGameTimer() - startTime
                if elapsedTime >= Config.xiaohaReviveTime - 50 then
                    local revivalSystem = GetRevivalSystem()
                    if revivalSystem == 'osp' then
                        TriggerEvent('osp_ambulance:revive')
                    elseif revivalSystem == 'esx' then
                        TriggerEvent('esx_ambulancejob:revive')
                    elseif revivalSystem == 'wasabi' then
                        TriggerEvent('wasabi_ambulance:revive')
                    elseif revivalSystem == 'qb' then
                        TriggerEvent('hospital:client:Revive')
                    end
                    StopScreenEffect('DeathFailOut')
                    
                    -- 记录使用AI医护的时间
                    lastReviveTime = GetGameTimer()
                    
                    -- 根据配置决定是否通知支付费用
                    if Config.xiaohaNotifyPayment then
                        SendNotification(string.format(Config.xiaohahenshuaiNotify.revive_complete, Config.xiaohaPrice))
                    end
                    
                    -- 新增聊天框通知
                    if Config.xiaohaChatNotify then
                        TriggerServerEvent('xiaohahenshuai:chatNotify')
                    end
                    
                    break
                end
            end
        end)
    end
end

-- -- 绘制屏幕上的文字
-- function DrawTextOnScreen(text, x, y)
-- 	SetTextFont(0)
-- 	SetTextProportional(1)
-- 	SetTextScale(0.5, 0.5)
-- 	SetTextColour(255, 255, 255, 255)
-- 	SetTextDropShadow(0, 0, 0, 0, 255)
-- 	SetTextEdge(1, 0, 0, 0, 255)
-- 	SetTextDropShadow()
-- 	SetTextOutline()
-- 	SetTextCentre(1)
-- 	SetTextEntry("STRING")
-- 	AddTextComponentString(text)
-- 	DrawText(x, y)
-- end
