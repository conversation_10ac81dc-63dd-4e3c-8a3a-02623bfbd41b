local ESX = nil
local QBCore = nil

-- 根据配置初始化框架
if Config.Framework == 'esx' then
    ESX = exports["es_extended"]:getSharedObject()
elseif Config.Framework == 'qb' then
    QBCore = exports['qb-core']:GetCoreObject()
end

function SendNotification(message, type)
    print("SendNotification 被调用")
    local notifyType = Config.xiaohaNotifyType

    if notifyType == 'auto' then
        if Config.Framework == 'qb' and QBCore then
            notifyType = 'qb'
            print("使用 QB-Core 通知系统")
        elseif Config.Framework == 'esx' and ESX and ESX.ShowNotification then
            notifyType = 'esx'
            print("使用 ESX 通知系统")
        elseif exports['mythic_notify'] then
            notifyType = 'mythic'
            print("使用 Mythic 通知系统")
        elseif exports['okokNotify'] then
            notifyType = 'okokNotify'
            print("使用 okokNotify 通知系统")
        else
            print("未找到可用的通知系统")
            return
        end
    end

    if notifyType == 'qb' then
        QBCore.Functions.Notify(message, type or 'primary')
    
    elseif notifyType == 'esx' then
        ESX.ShowNotification(message)
    
    elseif notifyType == 'mythic' then
        exports['mythic_notify']:DoHudText(type or 'inform', message)
    
    elseif notifyType == 'okokNotify' then
        exports['okokNotify']:Alert("系统", message, 5000, type or 'info')
    
    elseif notifyType == 'custom' then
        -- 在这里添加你的自定义通知系统
        -- TriggerEvent('xiaohahenshuai:notification', message, type)
    else
        print("未识别的通知类型: " .. tostring(notifyType))
    end
end